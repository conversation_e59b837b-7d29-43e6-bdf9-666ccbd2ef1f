<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>用户登录 - 智能对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
            margin: 20px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-title {
            font-size: 28px;
            font-weight: 600;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fff;
        }

        .form-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-input.error {
            border-color: #ff4757;
            box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
        }

        .error-message {
            color: #ff4757;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .login-button {
            width: 100%;
            padding: 14px;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
        }

        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 15px;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .footer-links {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }

        .footer-links a {
            color: #4facfe;
            text-decoration: none;
            font-size: 14px;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .login-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">🤖 智能助手</h1>
            <p class="login-subtitle">请登录您的账户</p>
        </div>

        <div id="alertContainer"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="phone" class="form-label">手机号</label>
                <input type="tel" id="phone" name="phone" class="form-input" placeholder="请输入手机号" maxlength="11">
                <div class="error-message" id="phoneError">请输入正确的手机号</div>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">密码</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码">
                <div class="error-message" id="passwordError">密码不能为空</div>
            </div>

            <button type="submit" class="login-button" id="loginButton">
                登录
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <span style="margin-left: 10px;">登录中...</span>
        </div>

        <div class="footer-links">
            <a href="/">返回首页</a>
        </div>
    </div>

    <script>
        class LoginApp {
            constructor() {
                this.form = document.getElementById('loginForm');
                this.phoneInput = document.getElementById('phone');
                this.passwordInput = document.getElementById('password');
                this.loginButton = document.getElementById('loginButton');
                this.loading = document.getElementById('loading');
                this.alertContainer = document.getElementById('alertContainer');
                
                this.init();
            }
            
            init() {
                this.form.addEventListener('submit', (e) => this.handleSubmit(e));
                this.phoneInput.addEventListener('input', () => this.validatePhone());
                this.passwordInput.addEventListener('input', () => this.validatePassword());
                
                // 自动聚焦手机号输入框
                this.phoneInput.focus();
            }
            
            validatePhone() {
                const phone = this.phoneInput.value.trim();
                const phoneError = document.getElementById('phoneError');
                const phoneRegex = /^1[3-9]\d{9}$/;
                
                if (phone === '') {
                    this.phoneInput.classList.remove('error');
                    phoneError.style.display = 'none';
                    return true;
                }
                
                if (!phoneRegex.test(phone)) {
                    this.phoneInput.classList.add('error');
                    phoneError.style.display = 'block';
                    return false;
                } else {
                    this.phoneInput.classList.remove('error');
                    phoneError.style.display = 'none';
                    return true;
                }
            }
            
            validatePassword() {
                const password = this.passwordInput.value.trim();
                const passwordError = document.getElementById('passwordError');
                
                if (password === '') {
                    this.passwordInput.classList.add('error');
                    passwordError.style.display = 'block';
                    return false;
                } else {
                    this.passwordInput.classList.remove('error');
                    passwordError.style.display = 'none';
                    return true;
                }
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const phone = this.phoneInput.value.trim();
                const password = this.passwordInput.value.trim();
                
                // 验证表单
                const isPhoneValid = this.validatePhone();
                const isPasswordValid = this.validatePassword();
                
                if (!isPhoneValid || !isPasswordValid) {
                    return;
                }
                
                if (phone === '' || password === '') {
                    this.showAlert('请填写完整的登录信息', 'error');
                    return;
                }
                
                this.setLoading(true);
                
                try {
                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            phone: phone,
                            password: password
                        })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        this.showAlert('登录成功，正在跳转...', 'success');
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } else {
                        this.showAlert(result.message || '登录失败', 'error');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    this.showAlert('网络错误，请稍后重试', 'error');
                } finally {
                    this.setLoading(false);
                }
            }
            
            setLoading(loading) {
                if (loading) {
                    this.loginButton.disabled = true;
                    this.loginButton.style.display = 'none';
                    this.loading.style.display = 'block';
                } else {
                    this.loginButton.disabled = false;
                    this.loginButton.style.display = 'block';
                    this.loading.style.display = 'none';
                }
            }
            
            showAlert(message, type) {
                this.alertContainer.innerHTML = `
                    <div class="alert alert-${type}">
                        ${message}
                    </div>
                `;
                
                const alert = this.alertContainer.querySelector('.alert');
                alert.style.display = 'block';
                
                // 3秒后自动隐藏错误提示
                if (type === 'error') {
                    setTimeout(() => {
                        if (alert) {
                            alert.style.display = 'none';
                        }
                    }, 3000);
                }
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new LoginApp();
        });
    </script>
</body>
</html>
