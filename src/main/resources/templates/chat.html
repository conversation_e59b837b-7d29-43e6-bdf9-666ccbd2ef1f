<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能对话助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chat-header {
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 20px;
            animation: fadeInUp 0.3s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            text-align: right;
        }

        .message.assistant {
            text-align: left;
        }

        .message-content {
            display: inline-block;
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
            word-wrap: break-word;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e8ed;
            border-bottom-left-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .typing-indicator {
            display: none;
            text-align: left;
            margin-bottom: 20px;
        }

        .typing-indicator .message-content {
            background: white;
            border: 1px solid #e1e8ed;
            padding: 12px 16px;
            border-radius: 18px;
            border-bottom-left-radius: 4px;
        }

        .typing-dots {
            display: inline-block;
        }

        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e8ed;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 44px;
            max-height: 120px;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 22px;
            font-size: 16px;
            font-family: inherit;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            line-height: 1.4;
        }

        .chat-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .send-button {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .send-button:hover:not(:disabled) {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .chat-container {
                height: 100vh;
                border-radius: 0;
            }

            .chat-header {
                padding: 12px 15px;
                font-size: 16px;
            }

            .chat-messages {
                padding: 15px;
            }

            .message-content {
                max-width: 85%;
                font-size: 16px;
            }

            .chat-input-container {
                padding: 15px;
            }

            .chat-input {
                font-size: 16px;
                min-height: 40px;
            }

            .send-button {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
        }

        /* 防止iOS Safari缩放 */
        @media screen and (max-width: 768px) {
            .chat-input {
                font-size: 16px !important;
            }
        }

        /* 滚动条样式 */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            🤖 智能对话助手
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    你好！我是你的智能助手，有什么可以帮助你的吗？
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
        
        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <textarea 
                    class="chat-input" 
                    id="chatInput" 
                    placeholder="输入你的问题..."
                    rows="1"
                ></textarea>
                <button type="submit" class="send-button" id="sendButton">
                    ➤
                </button>
            </form>
        </div>
    </div>

    <script>
        class ChatApp {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatForm = document.getElementById('chatForm');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.typingIndicator = document.getElementById('typingIndicator');
                
                this.isStreaming = false;
                this.currentAssistantMessage = null;
                
                this.init();
            }
            
            init() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.chatInput.addEventListener('input', () => this.autoResize());
                this.chatInput.addEventListener('keydown', (e) => this.handleKeyDown(e));
                
                // 自动聚焦输入框
                this.chatInput.focus();
            }
            
            handleKeyDown(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!this.isStreaming && this.chatInput.value.trim()) {
                        this.handleSubmit(e);
                    }
                }
            }
            
            autoResize() {
                this.chatInput.style.height = 'auto';
                this.chatInput.style.height = Math.min(this.chatInput.scrollHeight, 120) + 'px';
            }
            
            async handleSubmit(e) {
                e.preventDefault();
                
                const message = this.chatInput.value.trim();
                if (!message || this.isStreaming) return;
                
                // 添加用户消息
                this.addMessage(message, 'user');
                
                // 清空输入框
                this.chatInput.value = '';
                this.autoResize();
                
                // 禁用发送按钮
                this.setStreaming(true);
                
                // 显示打字指示器
                this.showTypingIndicator();
                
                try {
                    await this.streamResponse(message);
                } catch (error) {
                    console.error('Stream error:', error);
                    this.addMessage('抱歉，发生了错误，请稍后重试。', 'assistant');
                } finally {
                    this.setStreaming(false);
                    this.hideTypingIndicator();
                }
            }
            
            addMessage(content, sender) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = content;
                
                messageDiv.appendChild(contentDiv);
                this.chatMessages.appendChild(messageDiv);
                
                this.scrollToBottom();
                
                return contentDiv;
            }
            
            showTypingIndicator() {
                this.typingIndicator.style.display = 'block';
                this.scrollToBottom();
            }
            
            hideTypingIndicator() {
                this.typingIndicator.style.display = 'none';
            }
            
            async streamResponse(message) {
                // 隐藏打字指示器
                this.hideTypingIndicator();
                
                // 创建助手消息容器
                this.currentAssistantMessage = this.addMessage('', 'assistant');
                
                const response = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                try {
                    while (true) {
                        const { done, value } = await reader.read();
                        
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\n');
                        
                        let eventType = '';
                        for (const line of lines) {
                            if (line.startsWith('event:')) {
                                eventType = line.slice(6).trim();
                            } else if (line.startsWith('data:')) {
                                const data = line.slice(5).trim();
                                
                                if (eventType === 'message') {
                                    if (data) {
                                        this.appendToCurrentMessage(data);
                                    }
                                } else if (eventType === 'end') {
                                    return;
                                } else if (eventType === 'error') {
                                    throw new Error('Stream error');
                                }
                            } else if (line.trim() === '') {
                                // 空行，重置事件类型
                                eventType = '';
                            }
                        }
                    }
                } finally {
                    reader.releaseLock();
                }
            }
            
            appendToCurrentMessage(text) {
                if (this.currentAssistantMessage) {
                    this.currentAssistantMessage.textContent += text;
                    this.scrollToBottom();
                }
            }
            
            setStreaming(streaming) {
                this.isStreaming = streaming;
                this.sendButton.disabled = streaming;
                this.chatInput.disabled = streaming;
                
                if (streaming) {
                    this.sendButton.innerHTML = '⏸';
                } else {
                    this.sendButton.innerHTML = '➤';
                    this.chatInput.focus();
                }
            }
            
            scrollToBottom() {
                requestAnimationFrame(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                });
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
        
        // 防止iOS Safari的橡皮筋效果
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('.chat-messages')) {
                return;
            }
            e.preventDefault();
        }, { passive: false });
    </script>
</body>
</html>