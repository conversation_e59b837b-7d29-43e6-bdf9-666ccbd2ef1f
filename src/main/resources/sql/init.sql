-- 用户表
CREATE TABLE IF NOT EXISTS `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `organization_name` varchar(100) DEFAULT NULL COMMENT '机构名称',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-停用',
  `is_admin` tinyint NOT NULL DEFAULT '0' COMMENT '是否为管理员：1-是，0-否',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除：1-已删除，0-未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_is_admin` (`is_admin`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 插入默认管理员账户
-- 密码为：admin123
INSERT INTO `sys_user` (`phone`, `password`, `organization_name`, `status`, `is_admin`) 
VALUES ('13800138000', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfcjJSZr6Y2YdkqhKmBqKoSu', '系统管理', 1, 1)
ON DUPLICATE KEY UPDATE `phone` = `phone`;

-- 插入测试普通用户
-- 密码为：user123
INSERT INTO `sys_user` (`phone`, `password`, `organization_name`, `status`, `is_admin`) 
VALUES ('13800138001', '$2a$10$8.H9KmCqXn5.jvKoKvKoKOQb376NoUnuTJ8iKXIGfcjJSZr6Y2YdkqhKmBqKoSu', '测试机构', 1, 0)
ON DUPLICATE KEY UPDATE `phone` = `phone`;
