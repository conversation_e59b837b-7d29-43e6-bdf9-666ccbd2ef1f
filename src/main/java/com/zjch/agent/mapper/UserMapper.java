package com.zjch.agent.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zjch.agent.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据手机号查询用户
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND is_deleted = 0")
    User selectByPhone(String phone);
}
