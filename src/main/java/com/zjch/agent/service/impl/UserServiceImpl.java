package com.zjch.agent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zjch.agent.entity.User;
import com.zjch.agent.mapper.UserMapper;
import com.zjch.agent.service.UserService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public User login(String phone, String password) {
        User user = getUserByPhone(phone);
        if (user == null) {
            return null;
        }
        
        // 检查用户状态
        if (user.getStatus() == 0) {
            return null;
        }
        
        // 验证密码
        if (passwordEncoder.matches(password, user.getPassword())) {
            return user;
        }
        
        return null;
    }

    @Override
    public User getUserByPhone(String phone) {
        return baseMapper.selectByPhone(phone);
    }

    @Override
    public IPage<User> getUserPage(Page<User> page, String phone, String organizationName, Integer status) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StringUtils.hasText(phone), User::getPhone, phone)
               .like(StringUtils.hasText(organizationName), User::getOrganizationName, organizationName)
               .eq(status != null, User::getStatus, status)
               .orderByDesc(User::getCreateTime);
        
        return this.page(page, wrapper);
    }

    @Override
    public boolean createUser(User user) {
        // 加密密码
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        return this.save(user);
    }

    @Override
    public boolean updateUser(User user) {
        // 如果有新密码，则加密
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        return this.updateById(user);
    }

    @Override
    public boolean deleteUser(Long id) {
        return this.removeById(id);
    }

    @Override
    public boolean isPhoneExists(String phone, Long excludeId) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        if (excludeId != null) {
            wrapper.ne(User::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }
}
