package com.zjch.agent.controller;

import com.alibaba.dashscope.app.Application;
import com.alibaba.dashscope.app.ApplicationParam;
import com.alibaba.dashscope.app.ApplicationResult;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import io.reactivex.Flowable;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;

@Controller
public class ChatController {

    @GetMapping("/")
    public String index() {
        return "chat";
    }

    @PostMapping(value = "/api/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ResponseBody
    public SseEmitter streamChat(@RequestBody ChatRequest request) {
        SseEmitter emitter = new SseEmitter(Long.MAX_VALUE);
        
        CompletableFuture.runAsync(() -> {
            try {
                ApplicationParam param = ApplicationParam.builder()
                        .apiKey("sk-4dc7b44c2e6c4f88b9f5664d5a81708c")
                        .appId("3a1d03791d204389b76431c36cbc5dff")
                        .prompt(request.getMessage())
                        .incrementalOutput(true)
                        .build();
                
                Application application = new Application();
                Flowable<ApplicationResult> result = application.streamCall(param);
                
                result.blockingForEach(data -> {
                    try {
                        String text = data.getOutput().getText();
                        if (text != null && !text.isEmpty()) {
                            emitter.send(SseEmitter.event()
                                    .name("message")
                                    .data(text));
                        }
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                        throw new RuntimeException(e);
                    }
                });
                
                try {
                    emitter.send(SseEmitter.event()
                            .name("end")
                            .data(""));
                } catch (IOException e) {
                    emitter.completeWithError(e);
                    return;
                }
                emitter.complete();
                
            } catch (NoApiKeyException | InputRequiredException e) {
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("Error: " + e.getMessage()));
                } catch (IOException ioException) {
                    emitter.completeWithError(ioException);
                }
                emitter.complete();
            }
        });
        
        return emitter;
    }

    public static class ChatRequest {
        private String message;
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
    }
}