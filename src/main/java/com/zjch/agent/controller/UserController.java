package com.zjch.agent.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zjch.agent.entity.User;
import com.zjch.agent.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户管理控制器
 */
@Controller
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户管理页面
     */
    @GetMapping("/user-management")
    public String userManagementPage(HttpSession session) {
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            return "redirect:/login";
        }
        return "user-management";
    }

    /**
     * 分页查询用户列表
     */
    @GetMapping("/api/users")
    @ResponseBody
    public Map<String, Object> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String phone,
            @RequestParam(required = false) String organizationName,
            @RequestParam(required = false) Integer status,
            HttpSession session) {
        
        Map<String, Object> result = new HashMap<>();
        
        // 检查管理员权限
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            result.put("success", false);
            result.put("message", "无权限访问");
            return result;
        }
        
        try {
            Page<User> pageParam = new Page<>(page, size);
            IPage<User> userPage = userService.getUserPage(pageParam, phone, organizationName, status);
            
            result.put("success", true);
            result.put("data", userPage.getRecords());
            result.put("total", userPage.getTotal());
            result.put("current", userPage.getCurrent());
            result.put("size", userPage.getSize());
            result.put("pages", userPage.getPages());
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 创建用户
     */
    @PostMapping("/api/users")
    @ResponseBody
    public Map<String, Object> createUser(@RequestBody User user, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查管理员权限
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            result.put("success", false);
            result.put("message", "无权限访问");
            return result;
        }
        
        try {
            // 验证必填字段
            if (user.getPhone() == null || user.getPhone().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "手机号不能为空");
                return result;
            }
            
            if (user.getPassword() == null || user.getPassword().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "密码不能为空");
                return result;
            }
            
            // 验证手机号格式
            if (!user.getPhone().matches("^1[3-9]\\d{9}$")) {
                result.put("success", false);
                result.put("message", "手机号格式不正确");
                return result;
            }
            
            // 检查手机号是否已存在
            if (userService.isPhoneExists(user.getPhone(), null)) {
                result.put("success", false);
                result.put("message", "手机号已存在");
                return result;
            }
            
            // 设置默认值
            if (user.getStatus() == null) {
                user.setStatus(1);
            }
            if (user.getIsAdmin() == null) {
                user.setIsAdmin(0);
            }
            
            boolean success = userService.createUser(user);
            if (success) {
                result.put("success", true);
                result.put("message", "创建成功");
            } else {
                result.put("success", false);
                result.put("message", "创建失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 更新用户
     */
    @PutMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> updateUser(@PathVariable Long id, @RequestBody User user, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查管理员权限
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            result.put("success", false);
            result.put("message", "无权限访问");
            return result;
        }
        
        try {
            user.setId(id);
            
            // 验证必填字段
            if (user.getPhone() == null || user.getPhone().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "手机号不能为空");
                return result;
            }
            
            // 验证手机号格式
            if (!user.getPhone().matches("^1[3-9]\\d{9}$")) {
                result.put("success", false);
                result.put("message", "手机号格式不正确");
                return result;
            }
            
            // 检查手机号是否已存在（排除当前用户）
            if (userService.isPhoneExists(user.getPhone(), id)) {
                result.put("success", false);
                result.put("message", "手机号已存在");
                return result;
            }
            
            boolean success = userService.updateUser(user);
            if (success) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/api/users/{id}")
    @ResponseBody
    public Map<String, Object> deleteUser(@PathVariable Long id, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        // 检查管理员权限
        User currentUser = (User) session.getAttribute("user");
        if (currentUser == null || currentUser.getIsAdmin() != 1) {
            result.put("success", false);
            result.put("message", "无权限访问");
            return result;
        }
        
        try {
            // 不能删除自己
            if (currentUser.getId().equals(id)) {
                result.put("success", false);
                result.put("message", "不能删除自己");
                return result;
            }
            
            boolean success = userService.deleteUser(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        
        return result;
    }
}
