package com.zjch.agent.controller;

import com.zjch.agent.entity.User;
import com.zjch.agent.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.Map;

/**
 * 登录控制器
 */
@Controller
public class LoginController {

    @Autowired
    private UserService userService;

    /**
     * 显示登录页面
     */
    @GetMapping("/login")
    public String loginPage() {
        return "login";
    }

    /**
     * 处理登录请求
     */
    @PostMapping("/api/login")
    @ResponseBody
    public Map<String, Object> login(@RequestBody LoginRequest request, HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证参数
            if (request.getPhone() == null || request.getPhone().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "手机号不能为空");
                return result;
            }
            
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "密码不能为空");
                return result;
            }
            
            // 验证手机号格式
            if (!isValidPhone(request.getPhone())) {
                result.put("success", false);
                result.put("message", "手机号格式不正确");
                return result;
            }
            
            // 登录验证
            User user = userService.login(request.getPhone(), request.getPassword());
            if (user != null) {
                // 登录成功，保存用户信息到session
                session.setAttribute("user", user);
                result.put("success", true);
                result.put("message", "登录成功");
                result.put("isAdmin", user.getIsAdmin() == 1);
            } else {
                result.put("success", false);
                result.put("message", "手机号或密码错误，或账户已被停用");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登录失败：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 登出
     */
    @PostMapping("/api/logout")
    @ResponseBody
    public Map<String, Object> logout(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        try {
            session.invalidate();
            result.put("success", true);
            result.put("message", "登出成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "登出失败");
        }
        return result;
    }

    /**
     * 检查登录状态
     */
    @GetMapping("/api/check-login")
    @ResponseBody
    public Map<String, Object> checkLogin(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        User user = (User) session.getAttribute("user");
        if (user != null) {
            result.put("loggedIn", true);
            result.put("isAdmin", user.getIsAdmin() == 1);
            result.put("phone", user.getPhone());
            result.put("organizationName", user.getOrganizationName());
        } else {
            result.put("loggedIn", false);
        }
        return result;
    }

    /**
     * 验证手机号格式
     */
    private boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        // 简单的手机号验证：11位数字，以1开头
        return phone.matches("^1[3-9]\\d{9}$");
    }

    /**
     * 登录请求对象
     */
    public static class LoginRequest {
        private String phone;
        private String password;

        public String getPhone() {
            return phone;
        }

        public void setPhone(String phone) {
            this.phone = phone;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }
}
